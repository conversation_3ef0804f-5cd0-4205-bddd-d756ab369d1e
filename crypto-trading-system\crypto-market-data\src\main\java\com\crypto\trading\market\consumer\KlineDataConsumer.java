package com.crypto.trading.market.consumer;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MarketDataRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.retrytopic.TopicSuffixingStrategy;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * K线数据Kafka消费者
 * 专门负责消费原始K线数据并写入数据库
 * 使用Kafka的重试机制和死信队列确保数据可靠性
 */
@Component
public class KlineDataConsumer {

    private static final Logger log = LoggerFactory.getLogger(KlineDataConsumer.class);

    @Autowired
    private InfluxDBRepository influxDBRepository;

    @Autowired
    private MarketDataRepository mySQLMarketDataRepository;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 处理成功计数器
     */
    private final AtomicLong successCount = new AtomicLong(0);

    /**
     * 处理失败计数器
     */
    private final AtomicLong failureCount = new AtomicLong(0);

    /**
     * 消费K线原始数据并写入数据库
     * 使用重试机制和死信队列确保数据可靠性
     */
    @RetryableTopic(
            attempts = "3",
            backoff = @Backoff(delay = 1000, multiplier = 2.0),
            autoCreateTopics = "true",
            topicSuffixingStrategy = TopicSuffixingStrategy.SUFFIX_WITH_INDEX_VALUE,
            dltStrategy = org.springframework.kafka.retrytopic.DltStrategy.FAIL_ON_ERROR,
            include = {Exception.class}
    )
    @KafkaListener(
            topics = "kline.raw.data",
            groupId = "kline-database-consumer-group",
            containerFactory = "kafkaListenerContainerFactory"
    )
    public void consumeKlineData(
            @Payload Object message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            ConsumerRecord<String, Object> record,
            Acknowledgment acknowledgment) {

        String key = record.key();

        log.debug("接收到K线数据: topic={}, partition={}, offset={}, key={}",
                topic, partition, offset, key);

        try {
            // 记录消息类型和基本信息
            log.debug("开始处理K线数据: messageType={}, key={}",
                    message != null ? message.getClass().getSimpleName() : "null", key);

            // 处理不同类型的消息
            KlineDataDTO klineDataDTO;
            try {
                if (message instanceof String) {
                    // 如果是字符串，直接解析为KlineDataDTO
                    String messageStr = (String) message;
                    if (messageStr.trim().isEmpty()) {
                        log.warn("收到空字符串消息: key={}", key);
                        acknowledgment.acknowledge();
                        return;
                    }
                    klineDataDTO = objectMapper.readValue(messageStr, KlineDataDTO.class);
                } else if (message instanceof KlineDataDTO) {
                    // 如果已经是KlineDataDTO对象，直接使用
                    klineDataDTO = (KlineDataDTO) message;
                } else if (message instanceof Map) {
                    // 如果是Map，可能是包装的消息格式
                    Map<String, Object> messageMap = (Map<String, Object>) message;

                    if (messageMap.isEmpty()) {
                        log.warn("收到空Map消息: key={}", key);
                        acknowledgment.acknowledge();
                        return;
                    }

                    Object dataObj = messageMap.get("data");
                    if (dataObj != null) {
                        log.debug("从包装消息中提取data字段: key={}, messageType={}",
                                key, messageMap.get("messageType"));
                        klineDataDTO = objectMapper.convertValue(dataObj, KlineDataDTO.class);
                    } else {
                        log.debug("直接转换Map为KlineDataDTO: key={}", key);
                        // 直接将Map转换为KlineDataDTO
                        klineDataDTO = objectMapper.convertValue(messageMap, KlineDataDTO.class);
                    }
                } else {
                    // 尝试将对象转换为KlineDataDTO
                    log.debug("尝试转换对象为KlineDataDTO: key={}, objectType={}",
                            key, message.getClass().getSimpleName());
                    klineDataDTO = objectMapper.convertValue(message, KlineDataDTO.class);
                }
            } catch (Exception e) {
                log.error("K线数据解析异常: key={}, messageType={}, error={}",
                        key, message != null ? message.getClass().getSimpleName() : "null",
                        e.getMessage(), e);
                acknowledgment.acknowledge();
                return;
            }

            if (klineDataDTO == null) {
                log.warn("K线数据解析为空: key={}, messageType={}",
                        key, message != null ? message.getClass().getSimpleName() : "null");
                acknowledgment.acknowledge();
                return;
            }

            // 验证关键字段
            if (klineDataDTO.getSymbol() == null || klineDataDTO.getInterval() == null) {
                log.error("K线数据关键字段为null: symbol={}, interval={}, openTime={}, closeTime={}, key={}",
                        klineDataDTO.getSymbol(), klineDataDTO.getInterval(),
                        klineDataDTO.getOpenTime(), klineDataDTO.getCloseTime(), key);
                acknowledgment.acknowledge();
                return;
            }

            // 写入InfluxDB
            boolean influxSuccess = writeToInfluxDB(klineDataDTO);
            
            // 写入MySQL
            boolean mysqlSuccess = writeToMySQL(klineDataDTO);

            // 只有两个数据库都写入成功才确认消息
            if (influxSuccess && mysqlSuccess) {
                acknowledgment.acknowledge();
                successCount.incrementAndGet();
                
                log.debug("K线数据写入数据库成功: symbol={}, interval={}, openTime={}", 
                        klineDataDTO.getSymbol(), klineDataDTO.getInterval(), klineDataDTO.getOpenTime());
            } else {
                // 如果任何一个数据库写入失败，抛出异常触发重试
                String errorMsg = String.format("K线数据写入数据库失败: symbol=%s, interval=%s, influxSuccess=%s, mysqlSuccess=%s",
                        klineDataDTO.getSymbol(), klineDataDTO.getInterval(), influxSuccess, mysqlSuccess);
                throw new RuntimeException(errorMsg);
            }

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("处理K线数据异常: topic={}, partition={}, offset={}, key={}, error={}", 
                    topic, partition, offset, key, e.getMessage(), e);
            
            // 重新抛出异常，触发重试机制
            throw new RuntimeException("处理K线数据失败", e);
        }
    }

    /**
     * 写入InfluxDB
     */
    private boolean writeToInfluxDB(KlineDataDTO klineDataDTO) {
        try {
            influxDBRepository.saveKlineData(klineDataDTO);
            return true;
        } catch (Exception e) {
            log.error("写入K线数据到InfluxDB失败: symbol={}, interval={}, error={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 写入MySQL
     */
    private boolean writeToMySQL(KlineDataDTO klineDataDTO) {
        try {
            mySQLMarketDataRepository.saveKlineData(klineDataDTO);
            return true;
        } catch (Exception e) {
            log.error("写入K线数据到MySQL失败: symbol={}, interval={}, error={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理死信队列消息
     */
    @KafkaListener(
            topics = "kline.raw.data-dlt",
            groupId = "kline-database-consumer-group-dlt"
    )
    public void handleDltMessage(
            @Payload Object message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            ConsumerRecord<String, Object> record,
            Acknowledgment acknowledgment) {

        String key = record.key();
        String messageStr;

        try {
            // 处理不同类型的消息
            if (message instanceof String) {
                messageStr = (String) message;
            } else {
                // 如果是对象，转换为JSON字符串
                messageStr = objectMapper.writeValueAsString(message);
            }
        } catch (Exception e) {
            messageStr = message.toString();
            log.warn("DLT消息序列化失败，使用toString(): {}", e.getMessage());
        }

        log.error("K线数据进入死信队列: topic={}, partition={}, offset={}, key={}, message={}",
                topic, partition, offset, key, messageStr);

        // 这里可以实现特殊的处理逻辑，比如：
        // 1. 发送告警通知
        // 2. 写入特殊的错误日志表
        // 3. 尝试其他恢复策略

        // 确认死信队列消息，避免重复处理
        acknowledgment.acknowledge();
    }

    /**
     * 获取处理成功计数
     */
    public long getSuccessCount() {
        return successCount.get();
    }

    /**
     * 获取处理失败计数
     */
    public long getFailureCount() {
        return failureCount.get();
    }

    /**
     * 重置计数器
     */
    public void resetCounters() {
        successCount.set(0);
        failureCount.set(0);
    }
}
