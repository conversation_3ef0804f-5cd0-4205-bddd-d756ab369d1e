package com.crypto.trading.market.converter;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.common.dto.market.TradeDTO;
import com.crypto.trading.sdk.response.model.DepthData;
import com.crypto.trading.sdk.response.model.KlineData;
import com.crypto.trading.sdk.response.model.TradeData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * 市场数据转换器
 * <p>
 * 用于将SDK响应模型转换为系统DTO
 * </p>
 * <p>
 * 注意：该转换器负责统一命名约定，特别是：
 * - SDK模块的深度数据模型为DepthData
 * - DTO层的深度数据模型为OrderBookDTO
 * 这两个类表示相同的数据，但位于不同的模块中，命名不同
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class MarketDataConverter {

    /**
     * 将SDK深度数据转换为订单簿DTO
     * <p>
     * 统一命名：将DepthData转换为OrderBookDTO
     * </p>
     *
     * @param depthData SDK深度数据
     * @param symbol 交易对
     * @return 订单簿DTO
     */
    public DepthDataDTO convertToOrderBookDTO(DepthData depthData, String symbol) {
        if (depthData == null) {
            System.err.println("警告: DepthData为null，无法转换");
            return null;
        }

        if (symbol == null || symbol.trim().isEmpty()) {
            System.err.println("警告: symbol为空，使用默认值UNKNOWN");
            symbol = "UNKNOWN";
        }

        // 转换买单列表
        List<DepthDataDTO.PriceQuantity> bids = new ArrayList<>();
        if (depthData.getBids() != null) {
            for (DepthData.PriceQuantity priceQuantity : depthData.getBids()) {
                if (priceQuantity != null && priceQuantity.getPrice() != null && priceQuantity.getQuantity() != null) {
                    bids.add(new DepthDataDTO.PriceQuantity(priceQuantity.getPrice(), priceQuantity.getQuantity()));
                }
            }
        }

        // 转换卖单列表
        List<DepthDataDTO.PriceQuantity> asks = new ArrayList<>();
        if (depthData.getAsks() != null) {
            for (DepthData.PriceQuantity priceQuantity : depthData.getAsks()) {
                if (priceQuantity != null && priceQuantity.getPrice() != null && priceQuantity.getQuantity() != null) {
                    asks.add(new DepthDataDTO.PriceQuantity(priceQuantity.getPrice(), priceQuantity.getQuantity()));
                }
            }
        }

        // 转换更新时间
        LocalDateTime updateTime = depthData.getEventTime() != null ?
                LocalDateTime.ofInstant(
                        Instant.ofEpochMilli(depthData.getEventTime()),
                        ZoneId.systemDefault()) :
                LocalDateTime.now();

        // 创建并返回OrderBookDTO
        DepthDataDTO result = new DepthDataDTO(
                symbol,
                depthData.getLastUpdateId() != null ? depthData.getLastUpdateId() : 0L,
                bids.size(),
                bids,
                asks,
                updateTime
        );

        System.out.println("调试: 转换DepthDataDTO成功 - symbol=" + result.getSymbol() +
                          ", bids=" + result.getBids().size() +
                          ", asks=" + result.getAsks().size() +
                          ", updateTime=" + result.getUpdateTime());

        return result;
    }

    /**
     * 将WebSocket消息中的深度数据转换为订单簿DTO
     * <p>
     * 统一命名：将WebSocketMessage<DepthData>转换为OrderBookDTO
     * </p>
     *
     * @param message WebSocket消息
     * @return 订单簿DTO
     */
    public DepthDataDTO convertDepthMessageToOrderBookDTO(WebSocketMessage<DepthData> message) {
        if (message == null || message.getData() == null) {
            return null;
        }
        
        return convertToOrderBookDTO(message.getData(), message.getSymbol());
    }
    
    /**
     * 将SDK K线数据转换为K线DTO
     *
     * @param klineData SDK K线数据
     * @param symbol 交易对
     * @param interval K线间隔
     * @return K线DTO
     */
    public KlineDataDTO convertToKlineDataDTO(KlineData klineData, String symbol, String interval) {
        if (klineData == null) {
            return null;
        }

        // 检查必要的时间字段
        if (klineData.getOpenTime() == null || klineData.getCloseTime() == null) {
            // 记录警告并使用当前时间作为默认值
            System.err.println("警告: K线数据时间字段为null, symbol=" + symbol + ", interval=" + interval +
                             ", openTime=" + klineData.getOpenTime() + ", closeTime=" + klineData.getCloseTime());

            long currentTime = System.currentTimeMillis();
            Long openTime = klineData.getOpenTime() != null ? klineData.getOpenTime() : currentTime;
            Long closeTime = klineData.getCloseTime() != null ? klineData.getCloseTime() : currentTime;

            // 转换时间
            LocalDateTime openDateTime = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(openTime),
                    ZoneId.systemDefault());

            LocalDateTime closeDateTime = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(closeTime),
                    ZoneId.systemDefault());

            // 创建并返回KlineDataDTO
            return new KlineDataDTO(
                    symbol,
                    interval,
                    openDateTime,
                    closeDateTime,
                    klineData.getOpen() != null ? klineData.getOpen() : BigDecimal.ZERO,
                    klineData.getHigh() != null ? klineData.getHigh() : BigDecimal.ZERO,
                    klineData.getLow() != null ? klineData.getLow() : BigDecimal.ZERO,
                    klineData.getClose() != null ? klineData.getClose() : BigDecimal.ZERO,
                    klineData.getVolume() != null ? klineData.getVolume() : BigDecimal.ZERO,
                    klineData.getQuoteAssetVolume() != null ? klineData.getQuoteAssetVolume() : BigDecimal.ZERO,
                    klineData.getNumberOfTrades() != null ? klineData.getNumberOfTrades() : 0L,
                    klineData.getTakerBuyBaseAssetVolume() != null ? klineData.getTakerBuyBaseAssetVolume() : BigDecimal.ZERO,
                    klineData.getTakerBuyQuoteAssetVolume() != null ? klineData.getTakerBuyQuoteAssetVolume() : BigDecimal.ZERO,
                    true // 假设接收到的K线已完成
            );
        }

        // 正常情况：转换开盘时间和收盘时间
        LocalDateTime openTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(klineData.getOpenTime()),
                ZoneId.systemDefault());

        LocalDateTime closeTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(klineData.getCloseTime()),
                ZoneId.systemDefault());

        // 创建并返回KlineDataDTO
        return new KlineDataDTO(
                symbol,
                interval,
                openTime,
                closeTime,
                klineData.getOpen() != null ? klineData.getOpen() : BigDecimal.ZERO,
                klineData.getHigh() != null ? klineData.getHigh() : BigDecimal.ZERO,
                klineData.getLow() != null ? klineData.getLow() : BigDecimal.ZERO,
                klineData.getClose() != null ? klineData.getClose() : BigDecimal.ZERO,
                klineData.getVolume() != null ? klineData.getVolume() : BigDecimal.ZERO,
                klineData.getQuoteAssetVolume() != null ? klineData.getQuoteAssetVolume() : BigDecimal.ZERO,
                klineData.getNumberOfTrades() != null ? klineData.getNumberOfTrades() : 0L,
                klineData.getTakerBuyBaseAssetVolume() != null ? klineData.getTakerBuyBaseAssetVolume() : BigDecimal.ZERO,
                klineData.getTakerBuyQuoteAssetVolume() != null ? klineData.getTakerBuyQuoteAssetVolume() : BigDecimal.ZERO,
                true // 假设接收到的K线已完成
        );
    }
    
    /**
     * 将WebSocket消息中的K线数据转换为K线DTO
     *
     * @param message WebSocket消息
     * @param interval K线间隔
     * @return K线DTO
     */
    public KlineDataDTO convertKlineMessageToKlineDataDTO(WebSocketMessage<KlineData> message, String interval) {
        if (message == null || message.getData() == null) {
            return null;
        }
        
        return convertToKlineDataDTO(message.getData(), message.getSymbol(), interval);
    }
    
    /**
     * 将聚合交易数据转换为交易DTO
     *
     * @param message WebSocket消息
     * @return 交易DTO
     */
    public TradeDTO convertAggTradeMessageToTradeDTO(String message) {
        if (message == null) {
            return null;
        }

        try {
            // 解析WebSocket消息获取聚合交易数据
            com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(message);

            // 检查消息是否包含data字段（币安WebSocket消息格式）
            com.alibaba.fastjson2.JSONObject dataObject;
            if (jsonObject.containsKey("data")) {
                // 嵌套格式：{"stream": "...", "data": {...}}
                dataObject = jsonObject.getJSONObject("data");
            } else {
                // 直接格式：{"e": "aggTrade", ...}
                dataObject = jsonObject;
            }

            String symbol = dataObject.getString("s");
            long tradeId = dataObject.getLongValue("a");
            BigDecimal price = new BigDecimal(dataObject.getString("p"));
            BigDecimal quantity = new BigDecimal(dataObject.getString("q"));
            long tradeTime = dataObject.getLongValue("T");
            boolean isBuyerMarketMaker = dataObject.getBooleanValue("m");

            // 转换交易时间
            LocalDateTime tradeDateTime = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(tradeTime),
                    ZoneId.systemDefault());

            // 计算成交额（价格 * 数量）
            BigDecimal quoteQuantity = price.multiply(quantity);

            // 创建并返回TradeDTO
            return new TradeDTO(
                    tradeId,
                    symbol,
                    price,
                    quantity,
                    quoteQuantity,
                    tradeDateTime,
                    isBuyerMarketMaker,
                    true // 假设所有成交都是最优价格匹配
            );
        } catch (Exception e) {
            // 记录错误并返回null
            return null;
        }
    }
    
    /**
     * 将SDK K线数据转换为K线DTO
     *
     * @param klineData SDK K线数据
     * @return K线DTO
     */
    public KlineDataDTO convertToKlineDTO(KlineData klineData) {
        if (klineData == null) {
            return null;
        }
        
        return convertToKlineDataDTO(klineData, klineData.getSymbol(), klineData.getInterval());
    }
    
    /**
     * 将SDK交易数据转换为交易DTO
     *
     * @param tradeData SDK交易数据
     * @return 交易DTO
     */
    public TradeDTO convertToTradeDTO(TradeData tradeData) {
        if (tradeData == null) {
            return null;
        }
        
        // 转换交易时间
        LocalDateTime tradeDateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(tradeData.getTime()),
                ZoneId.systemDefault());
        
        // 创建并返回TradeDTO
        return new TradeDTO(
                tradeData.getId(),
                tradeData.getSymbol(),
                tradeData.getPrice(),
                tradeData.getQuantity(), // 使用getQuantity替代getQty
                tradeData.getQuoteQuantity(), // 使用getQuoteQuantity替代getQuoteQty
                tradeDateTime,
                tradeData.isBuyerMaker(),
                tradeData.isBestMatch()
        );
    }
    
    /**
     * 将SDK深度数据转换为深度DTO
     *
     * @param depthData SDK深度数据
     * @return 深度DTO
     */
    public DepthDataDTO convertToDepthDTO(DepthData depthData) {
        if (depthData == null) {
            return null;
        }
        
        // 使用传入的symbol参数或默认值
        String symbol = "UNKNOWN"; // 默认值，实际使用中应该通过参数传递交易对信息
        
        return convertToOrderBookDTO(depthData, symbol);
    }
} 