package com.crypto.trading.market.consumer;

import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MarketDataRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.retrytopic.TopicSuffixingStrategy;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 深度数据Kafka消费者
 * 专门负责消费原始深度数据并写入数据库
 * 使用Kafka的重试机制和死信队列确保数据可靠性
 */
@Component
public class DepthDataConsumer {

    private static final Logger log = LoggerFactory.getLogger(DepthDataConsumer.class);

    @Autowired
    private InfluxDBRepository influxDBRepository;

    @Autowired
    private MarketDataRepository mySQLMarketDataRepository;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 处理成功计数器
     */
    private final AtomicLong successCount = new AtomicLong(0);

    /**
     * 处理失败计数器
     */
    private final AtomicLong failureCount = new AtomicLong(0);

    /**
     * 消费深度原始数据并写入数据库
     * 使用重试机制和死信队列确保数据可靠性
     */
    @RetryableTopic(
            attempts = "3",
            backoff = @Backoff(delay = 1000, multiplier = 2.0),
            autoCreateTopics = "true",
            topicSuffixingStrategy = TopicSuffixingStrategy.SUFFIX_WITH_INDEX_VALUE,
            dltStrategy = org.springframework.kafka.retrytopic.DltStrategy.FAIL_ON_ERROR,
            include = {Exception.class}
    )
    @KafkaListener(
            topics = "depth.raw.data",
            groupId = "depth-database-consumer-group",
            containerFactory = "kafkaListenerContainerFactory"
    )
    public void consumeDepthData(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            ConsumerRecord<String, String> record,
            Acknowledgment acknowledgment) {

        String key = record.key();
        
        log.debug("接收到深度数据: topic={}, partition={}, offset={}, key={}", 
                topic, partition, offset, key);

        try {
            // 验证消息内容
            if (message == null || message.trim().isEmpty()) {
                log.warn("收到空的深度数据消息: key={}", key);
                acknowledgment.acknowledge();
                return;
            }

            log.debug("开始处理深度数据: key={}, messageLength={}", key, message.length());

            // 解析JSON消息
            DepthDataDTO depthDataDTO;
            try {
                depthDataDTO = objectMapper.readValue(message, DepthDataDTO.class);
            } catch (Exception e) {
                log.error("深度数据JSON解析异常: key={}, messagePreview={}, error={}",
                        key, message.substring(0, Math.min(200, message.length())),
                        e.getMessage(), e);
                acknowledgment.acknowledge();
                return;
            }

            if (depthDataDTO == null) {
                log.warn("深度数据解析为空: key={}, messagePreview={}",
                        key, message.substring(0, Math.min(100, message.length())));
                acknowledgment.acknowledge();
                return;
            }

            // 验证关键字段
            if (depthDataDTO.getSymbol() == null) {
                log.error("深度数据symbol为null: key={}, lastUpdateId={}, updateTime={}",
                        key, depthDataDTO.getLastUpdateId(), depthDataDTO.getUpdateTime());
                acknowledgment.acknowledge();
                return;
            }

            // 验证买卖盘数据
            if ((depthDataDTO.getBids() == null || depthDataDTO.getBids().isEmpty()) &&
                (depthDataDTO.getAsks() == null || depthDataDTO.getAsks().isEmpty())) {
                log.warn("深度数据买卖盘都为空: symbol={}, key={}", depthDataDTO.getSymbol(), key);
                // 这种情况下仍然处理，可能是市场暂停等情况
            }

            // 写入InfluxDB
            boolean influxSuccess = writeToInfluxDB(depthDataDTO);
            
            // 写入MySQL
            boolean mysqlSuccess = writeToMySQL(depthDataDTO);

            // 只有两个数据库都写入成功才确认消息
            if (influxSuccess && mysqlSuccess) {
                acknowledgment.acknowledge();
                successCount.incrementAndGet();
                
                log.debug("深度数据写入数据库成功: symbol={}, updateTime={}", 
                        depthDataDTO.getSymbol(), depthDataDTO.getUpdateTime());
            } else {
                // 如果任何一个数据库写入失败，抛出异常触发重试
                String errorMsg = String.format("深度数据写入数据库失败: symbol=%s, influxSuccess=%s, mysqlSuccess=%s",
                        depthDataDTO.getSymbol(), influxSuccess, mysqlSuccess);
                throw new RuntimeException(errorMsg);
            }

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("处理深度数据异常: topic={}, partition={}, offset={}, key={}, error={}", 
                    topic, partition, offset, key, e.getMessage(), e);
            
            // 重新抛出异常，触发重试机制
            throw new RuntimeException("处理深度数据失败", e);
        }
    }

    /**
     * 写入InfluxDB
     */
    private boolean writeToInfluxDB(DepthDataDTO depthDataDTO) {
        try {
            influxDBRepository.saveDepthData(depthDataDTO);
            return true;
        } catch (Exception e) {
            log.error("写入深度数据到InfluxDB失败: symbol={}, error={}", 
                    depthDataDTO.getSymbol(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 写入MySQL
     */
    private boolean writeToMySQL(DepthDataDTO depthDataDTO) {
        try {
            mySQLMarketDataRepository.saveDepthData(depthDataDTO);
            return true;
        } catch (Exception e) {
            log.error("写入深度数据到MySQL失败: symbol={}, error={}", 
                    depthDataDTO.getSymbol(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理死信队列消息
     */
    @KafkaListener(
            topics = "depth.raw.data-dlt",
            groupId = "depth-database-consumer-group-dlt"
    )
    public void handleDltMessage(
            @Payload Object message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            ConsumerRecord<String, Object> record,
            Acknowledgment acknowledgment) {

        String key = record.key();
        String messageStr;

        try {
            // 处理不同类型的消息
            if (message instanceof String) {
                messageStr = (String) message;
            } else {
                // 如果是对象，转换为JSON字符串
                messageStr = objectMapper.writeValueAsString(message);
            }
        } catch (Exception e) {
            messageStr = message.toString();
            log.warn("DLT消息序列化失败，使用toString(): {}", e.getMessage());
        }

        log.error("深度数据进入死信队列: topic={}, partition={}, offset={}, key={}, message={}",
                topic, partition, offset, key, messageStr);

        // 这里可以实现特殊的处理逻辑，比如：
        // 1. 发送告警通知
        // 2. 写入特殊的错误日志表
        // 3. 尝试其他恢复策略

        // 确认死信队列消息，避免重复处理
        acknowledgment.acknowledge();
    }

    /**
     * 获取处理成功计数
     */
    public long getSuccessCount() {
        return successCount.get();
    }

    /**
     * 获取处理失败计数
     */
    public long getFailureCount() {
        return failureCount.get();
    }

    /**
     * 重置计数器
     */
    public void resetCounters() {
        successCount.set(0);
        failureCount.set(0);
    }
}
