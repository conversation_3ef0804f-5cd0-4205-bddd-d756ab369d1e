package com.crypto.trading.market.debug;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 序列化测试器
 * 用于测试DTO对象的序列化和反序列化
 */
public class SerializationTester {
    
    private static final Logger log = LoggerFactory.getLogger(SerializationTester.class);
    
    public static void main(String[] args) {
        testKlineDataSerialization();
        testDepthDataSerialization();
        testMessageWrapperSerialization();
    }
    
    /**
     * 测试K线数据序列化
     */
    public static void testKlineDataSerialization() {
        log.info("=== 测试K线数据序列化 ===");
        
        try {
            ObjectMapper objectMapper = createObjectMapper();
            
            // 创建测试数据
            KlineDataDTO klineData = new KlineDataDTO(
                "BTCUSDT",
                "1m",
                LocalDateTime.now().minusMinutes(1),
                LocalDateTime.now(),
                new BigDecimal("50000.00"),
                new BigDecimal("50100.00"),
                new BigDecimal("49900.00"),
                new BigDecimal("50050.00"),
                new BigDecimal("100.5"),
                new BigDecimal("5000000.00"),
                1000L,
                new BigDecimal("50.25"),
                new BigDecimal("2500000.00"),
                true
            );
            
            // 序列化
            String json = objectMapper.writeValueAsString(klineData);
            log.info("K线数据序列化结果: {}", json);
            
            // 反序列化
            KlineDataDTO deserializedData = objectMapper.readValue(json, KlineDataDTO.class);
            log.info("K线数据反序列化成功: symbol={}, interval={}", 
                    deserializedData.getSymbol(), deserializedData.getInterval());
            
            // 测试消息包装
            Map<String, Object> message = new HashMap<>();
            message.put("messageId", "test-123");
            message.put("messageType", "kline_raw");
            message.put("timestamp", System.currentTimeMillis());
            message.put("source", "websocket");
            message.put("data", klineData);
            
            String wrappedJson = objectMapper.writeValueAsString(message);
            log.info("包装消息序列化结果: {}", wrappedJson);
            
        } catch (Exception e) {
            log.error("K线数据序列化测试失败", e);
        }
    }
    
    /**
     * 测试深度数据序列化
     */
    public static void testDepthDataSerialization() {
        log.info("=== 测试深度数据序列化 ===");
        
        try {
            ObjectMapper objectMapper = createObjectMapper();
            
            // 创建测试数据
            List<DepthDataDTO.PriceQuantity> bids = new ArrayList<>();
            bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("49999.00"), new BigDecimal("1.5")));
            bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("49998.00"), new BigDecimal("2.0")));
            
            List<DepthDataDTO.PriceQuantity> asks = new ArrayList<>();
            asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("50001.00"), new BigDecimal("1.2")));
            asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("50002.00"), new BigDecimal("1.8")));
            
            DepthDataDTO depthData = new DepthDataDTO(
                "BTCUSDT",
                123456789L,
                20,
                bids,
                asks,
                LocalDateTime.now()
            );
            
            // 序列化
            String json = objectMapper.writeValueAsString(depthData);
            log.info("深度数据序列化结果: {}", json);
            
            // 反序列化
            DepthDataDTO deserializedData = objectMapper.readValue(json, DepthDataDTO.class);
            log.info("深度数据反序列化成功: symbol={}, bidsSize={}, asksSize={}", 
                    deserializedData.getSymbol(), 
                    deserializedData.getBids().size(), 
                    deserializedData.getAsks().size());
            
        } catch (Exception e) {
            log.error("深度数据序列化测试失败", e);
        }
    }
    
    /**
     * 测试消息包装序列化
     */
    public static void testMessageWrapperSerialization() {
        log.info("=== 测试消息包装序列化 ===");
        
        try {
            ObjectMapper objectMapper = createObjectMapper();
            
            // 测试空对象
            Map<String, Object> emptyMessage = new HashMap<>();
            String emptyJson = objectMapper.writeValueAsString(emptyMessage);
            log.info("空消息序列化结果: {}", emptyJson);
            
            // 测试null数据
            Map<String, Object> nullDataMessage = new HashMap<>();
            nullDataMessage.put("data", null);
            String nullDataJson = objectMapper.writeValueAsString(nullDataMessage);
            log.info("null数据消息序列化结果: {}", nullDataJson);
            
        } catch (Exception e) {
            log.error("消息包装序列化测试失败", e);
        }
    }
    
    /**
     * 创建ObjectMapper
     */
    private static ObjectMapper createObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }
}
