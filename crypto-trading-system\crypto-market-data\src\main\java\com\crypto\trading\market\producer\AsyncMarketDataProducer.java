package com.crypto.trading.market.producer;

import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.dto.market.TradeDTO;
import com.crypto.trading.market.config.AsyncKafkaProducerConfig;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 异步市场数据生产者
 * 负责将WebSocket接收到的原始数据发送到Kafka主题
 * 
 * 设计原则：
 * 1. 数据不丢失 - 使用可靠的Kafka配置
 * 2. 高性能 - 异步发送，批量处理
 * 3. 可监控 - 详细的指标统计
 * 4. 容错性 - 完善的错误处理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class AsyncMarketDataProducer {

    private static final Logger log = LoggerFactory.getLogger(AsyncMarketDataProducer.class);

    @Autowired
    @Qualifier("reliableKafkaTemplate")
    private KafkaTemplate<String, Object> reliableKafkaTemplate;

    @Autowired
    private AsyncKafkaProducerConfig kafkaConfig;

    @Autowired
    private ObjectMapper objectMapper;

    // 统计指标
    private final AtomicLong klineMessageCount = new AtomicLong(0);
    private final AtomicLong depthMessageCount = new AtomicLong(0);
    private final AtomicLong tradeMessageCount = new AtomicLong(0);
    private final AtomicLong successCount = new AtomicLong(0);
    private final AtomicLong failureCount = new AtomicLong(0);

    /**
     * 发送K线原始数据到Kafka
     * 
     * @param klineData K线数据
     * @return 发送结果的CompletableFuture
     */
    public CompletableFuture<SendResult<String, Object>> sendKlineRawData(KlineDataDTO klineData) {
        try {
            // 检查输入数据
            if (klineData == null) {
                log.error("K线数据为null，无法发送到Kafka");
                CompletableFuture<SendResult<String, Object>> failedFuture = new CompletableFuture<>();
                failedFuture.completeExceptionally(new IllegalArgumentException("K线数据为null"));
                return failedFuture;
            }

            log.debug("准备发送K线数据: symbol={}, interval={}, openTime={}, closeTime={}",
                    klineData.getSymbol(), klineData.getInterval(),
                    klineData.getOpenTime(), klineData.getCloseTime());

            // 构建消息
            Map<String, Object> message = buildKlineMessage(klineData);

            // 生成消息键（用于分区）
            String key = generateMessageKey(klineData.getSymbol(), "kline");

            log.debug("K线消息构建完成: key={}, messageId={}, dataType={}",
                    key, message.get("messageId"), message.get("messageType"));

            // 异步发送消息
            CompletableFuture<SendResult<String, Object>> future = reliableKafkaTemplate
                .send(kafkaConfig.getKlineRawTopic(), key, message);
            
            // 添加回调处理
            future.whenComplete((result, throwable) -> {
                if (throwable == null) {
                    successCount.incrementAndGet();
                    klineMessageCount.incrementAndGet();
                    log.debug("K线数据发送成功: symbol={}, interval={}, partition={}, offset={}", 
                        klineData.getSymbol(), klineData.getInterval(),
                        result.getRecordMetadata().partition(), result.getRecordMetadata().offset());
                } else {
                    failureCount.incrementAndGet();
                    log.error("K线数据发送失败: symbol={}, interval={}, error={}", 
                        klineData.getSymbol(), klineData.getInterval(), throwable.getMessage(), throwable);
                }
            });
            
            return future;
            
        } catch (Exception e) {
            log.error("构建K线消息失败: symbol={}, interval={}, error={}", 
                klineData.getSymbol(), klineData.getInterval(), e.getMessage(), e);
            
            // 返回失败的Future
            CompletableFuture<SendResult<String, Object>> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }
    }

    /**
     * 发送深度原始数据到Kafka
     * 
     * @param depthData 深度数据
     * @return 发送结果的CompletableFuture
     */
    public CompletableFuture<SendResult<String, Object>> sendDepthRawData(DepthDataDTO depthData) {
        try {
            // 检查输入数据
            if (depthData == null) {
                log.error("深度数据为null，无法发送到Kafka");
                CompletableFuture<SendResult<String, Object>> failedFuture = new CompletableFuture<>();
                failedFuture.completeExceptionally(new IllegalArgumentException("深度数据为null"));
                return failedFuture;
            }

            log.debug("准备发送深度数据: symbol={}, lastUpdateId={}, bidsSize={}, asksSize={}, updateTime={}",
                    depthData.getSymbol(), depthData.getLastUpdateId(),
                    depthData.getBids() != null ? depthData.getBids().size() : 0,
                    depthData.getAsks() != null ? depthData.getAsks().size() : 0,
                    depthData.getUpdateTime());

            // 构建消息
            Map<String, Object> message = buildDepthMessage(depthData);

            // 生成消息键
            String key = generateMessageKey(depthData.getSymbol(), "depth");

            log.debug("深度消息构建完成: key={}, messageId={}, dataType={}",
                    key, message.get("messageId"), message.get("messageType"));

            // 异步发送消息
            CompletableFuture<SendResult<String, Object>> future = reliableKafkaTemplate
                .send(kafkaConfig.getDepthRawTopic(), key, message);
            
            // 添加回调处理
            future.whenComplete((result, throwable) -> {
                if (throwable == null) {
                    successCount.incrementAndGet();
                    depthMessageCount.incrementAndGet();
                    log.debug("深度数据发送成功: symbol={}, partition={}, offset={}", 
                        depthData.getSymbol(), result.getRecordMetadata().partition(), 
                        result.getRecordMetadata().offset());
                } else {
                    failureCount.incrementAndGet();
                    log.error("深度数据发送失败: symbol={}, error={}", 
                        depthData.getSymbol(), throwable.getMessage(), throwable);
                }
            });
            
            return future;
            
        } catch (Exception e) {
            log.error("构建深度消息失败: symbol={}, error={}", 
                depthData.getSymbol(), e.getMessage(), e);
            
            CompletableFuture<SendResult<String, Object>> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }
    }

    /**
     * 发送交易原始数据到Kafka
     * 
     * @param tradeData 交易数据
     * @return 发送结果的CompletableFuture
     */
    public CompletableFuture<SendResult<String, Object>> sendTradeRawData(TradeDTO tradeData) {
        try {
            // 构建消息
            Map<String, Object> message = buildTradeMessage(tradeData);
            
            // 生成消息键
            String key = generateMessageKey(tradeData.getSymbol(), "trade");
            
            // 异步发送消息
            CompletableFuture<SendResult<String, Object>> future = reliableKafkaTemplate
                .send(kafkaConfig.getTradeRawTopic(), key, message);
            
            // 添加回调处理
            future.whenComplete((result, throwable) -> {
                if (throwable == null) {
                    successCount.incrementAndGet();
                    tradeMessageCount.incrementAndGet();
                    log.debug("交易数据发送成功: symbol={}, tradeId={}, partition={}, offset={}", 
                        tradeData.getSymbol(), tradeData.getId(),
                        result.getRecordMetadata().partition(), result.getRecordMetadata().offset());
                } else {
                    failureCount.incrementAndGet();
                    log.error("交易数据发送失败: symbol={}, tradeId={}, error={}", 
                        tradeData.getSymbol(), tradeData.getId(), throwable.getMessage(), throwable);
                }
            });
            
            return future;
            
        } catch (Exception e) {
            log.error("构建交易消息失败: symbol={}, tradeId={}, error={}", 
                tradeData.getSymbol(), tradeData.getId(), e.getMessage(), e);
            
            CompletableFuture<SendResult<String, Object>> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }
    }

    /**
     * 构建K线消息
     */
    private Map<String, Object> buildKlineMessage(KlineDataDTO klineData) {
        try {
            Map<String, Object> message = new HashMap<>();
            String messageId = generateMessageId();

            message.put("messageId", messageId);
            message.put("messageType", "kline_raw");
            message.put("timestamp", System.currentTimeMillis());
            message.put("source", "websocket");
            message.put("data", klineData);

            log.debug("构建K线消息: messageId={}, symbol={}, dataNotNull={}",
                    messageId,
                    klineData != null ? klineData.getSymbol() : "null",
                    klineData != null);

            // 验证消息内容
            if (klineData == null) {
                log.warn("警告: K线数据为null，消息中data字段将为null");
            }

            return message;
        } catch (Exception e) {
            log.error("构建K线消息失败", e);
            throw e;
        }
    }

    /**
     * 构建深度消息
     */
    private Map<String, Object> buildDepthMessage(DepthDataDTO depthData) {
        try {
            Map<String, Object> message = new HashMap<>();
            String messageId = generateMessageId();

            message.put("messageId", messageId);
            message.put("messageType", "depth_raw");
            message.put("timestamp", System.currentTimeMillis());
            message.put("source", "websocket");
            message.put("data", depthData);

            log.debug("构建深度消息: messageId={}, symbol={}, dataNotNull={}",
                    messageId,
                    depthData != null ? depthData.getSymbol() : "null",
                    depthData != null);

            // 验证消息内容
            if (depthData == null) {
                log.warn("警告: 深度数据为null，消息中data字段将为null");
            }

            return message;
        } catch (Exception e) {
            log.error("构建深度消息失败", e);
            throw e;
        }
    }

    /**
     * 构建交易消息
     */
    private Map<String, Object> buildTradeMessage(TradeDTO tradeData) {
        Map<String, Object> message = new HashMap<>();
        message.put("messageId", generateMessageId());
        message.put("messageType", "trade_raw");
        message.put("timestamp", System.currentTimeMillis());
        message.put("source", "websocket");
        message.put("data", tradeData);
        return message;
    }

    /**
     * 生成消息键，用于Kafka分区
     */
    private String generateMessageKey(String symbol, String dataType) {
        return String.format("%s_%s_%d", symbol, dataType, System.currentTimeMillis() % 1000);
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return String.format("msg_%d_%d", System.currentTimeMillis(), Thread.currentThread().getId());
    }

    /**
     * 获取统计信息
     */
    public Map<String, Long> getStatistics() {
        Map<String, Long> stats = new HashMap<>();
        stats.put("klineMessageCount", klineMessageCount.get());
        stats.put("depthMessageCount", depthMessageCount.get());
        stats.put("tradeMessageCount", tradeMessageCount.get());
        stats.put("successCount", successCount.get());
        stats.put("failureCount", failureCount.get());
        stats.put("totalMessageCount", klineMessageCount.get() + depthMessageCount.get() + tradeMessageCount.get());
        return stats;
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        klineMessageCount.set(0);
        depthMessageCount.set(0);
        tradeMessageCount.set(0);
        successCount.set(0);
        failureCount.set(0);
    }
}
