package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.AsyncMarketDataProducer;
import com.crypto.trading.sdk.response.model.DepthData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 深度数据处理器 - 重构版本
 * 负责处理从WebSocket接收到的深度数据，使用新的异步生产者发送到Kafka
 *
 * 重构要点：
 * 1. 移除虚拟线程直接写入数据库的逻辑
 * 2. 使用AsyncMarketDataProducer发送原始数据到Kafka
 * 3. 完善的错误处理和监控
 * 4. 确保数据不丢失
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class DepthDataProcessor {

    private static final Logger log = LoggerFactory.getLogger(DepthDataProcessor.class);

    @Autowired
    private AsyncMarketDataProducer asyncMarketDataProducer;

    @Autowired
    private MarketDataConverter marketDataConverter;

    // 统计指标
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong successCount = new AtomicLong(0);
    private final AtomicLong failureCount = new AtomicLong(0);

    /**
     * 处理深度数据 - 重构后的异步处理实现
     *
     * @param message WebSocket深度数据消息
     */
    public void processDepthData(WebSocketMessage<DepthData> message) {
        if (message == null || message.getData() == null) {
            log.warn("收到空的深度数据消息");
            return;
        }

        DepthData depthData = message.getData();
        String symbol = message.getSymbol();

        processedCount.incrementAndGet();

        log.debug("开始处理深度数据: symbol={}, lastUpdateId={}, bids={}, asks={}",
                symbol,
                depthData.getLastUpdateId(),
                depthData.getBids() != null ? depthData.getBids().size() : 0,
                depthData.getAsks() != null ? depthData.getAsks().size() : 0);

        try {
            // 验证输入参数
            if (depthData == null) {
                log.error("深度数据为null: symbol={}", symbol);
                failureCount.incrementAndGet();
                return;
            }

            if (symbol == null || symbol.trim().isEmpty()) {
                log.error("交易对symbol为空: depthData={}", depthData);
                failureCount.incrementAndGet();
                return;
            }

            log.debug("开始处理深度数据: symbol={}, lastUpdateId={}, bidsSize={}, asksSize={}",
                    symbol, depthData.getLastUpdateId(),
                    depthData.getBids() != null ? depthData.getBids().size() : 0,
                    depthData.getAsks() != null ? depthData.getAsks().size() : 0);

            // 转换为DepthDataDTO
            DepthDataDTO depthDataDTO;
            try {
                depthDataDTO = marketDataConverter.convertToOrderBookDTO(depthData, symbol);
            } catch (Exception e) {
                log.error("深度数据转换异常: symbol={}, error={}", symbol, e.getMessage(), e);
                failureCount.incrementAndGet();
                return;
            }

            if (depthDataDTO == null) {
                log.warn("深度数据转换返回null: symbol={}, lastUpdateId={}",
                        symbol, depthData.getLastUpdateId());
                failureCount.incrementAndGet();
                return;
            }

            // 验证转换后的数据
            if (depthDataDTO.getSymbol() == null) {
                log.error("转换后的深度数据symbol为null: originalSymbol={}, lastUpdateId={}",
                        symbol, depthDataDTO.getLastUpdateId());
                failureCount.incrementAndGet();
                return;
            }

            // 使用异步生产者发送原始数据到Kafka
            CompletableFuture<SendResult<String, Object>> future =
                asyncMarketDataProducer.sendDepthRawData(depthDataDTO);

            // 添加回调处理
            future.whenComplete((result, throwable) -> {
                if (throwable == null) {
                    successCount.incrementAndGet();
                    log.debug("深度数据发送成功: symbol={}, lastUpdateId={}, partition={}, offset={}",
                        symbol, depthDataDTO.getLastUpdateId(),
                        result.getRecordMetadata().partition(), result.getRecordMetadata().offset());
                } else {
                    failureCount.incrementAndGet();
                    log.error("深度数据发送失败: symbol={}, lastUpdateId={}, error={}",
                        symbol, depthDataDTO.getLastUpdateId(), throwable.getMessage(), throwable);

                    // 处理发送失败的情况
                    handleSendFailure(depthDataDTO, throwable);
                }
            });

            log.debug("深度数据已提交到异步队列: symbol={}, lastUpdateId={}",
                    symbol, depthDataDTO.getLastUpdateId());

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("处理深度数据异常: symbol={}, error={}", symbol, e.getMessage(), e);
        }
    }

    /**
     * 处理发送失败的情况
     *
     * @param depthDataDTO 失败的深度数据
     * @param throwable 异常信息
     */
    private void handleSendFailure(DepthDataDTO depthDataDTO, Throwable throwable) {
        log.error("深度数据发送失败，需要人工处理: symbol={}, lastUpdateId={}, error={}",
            depthDataDTO.getSymbol(), depthDataDTO.getLastUpdateId(), throwable.getMessage());

        // TODO: 实现失败数据的恢复机制
        // 1. 可以将失败的数据写入本地文件
        // 2. 可以发送到死信队列
        // 3. 可以实现重试机制
    }

    /**
     * 获取处理统计信息
     *
     * @return 统计信息
     */
    public String getStatistics() {
        return String.format("DepthDataProcessor统计: 处理总数=%d, 成功=%d, 失败=%d, 成功率=%.2f%%",
            processedCount.get(), successCount.get(), failureCount.get(),
            processedCount.get() > 0 ? (successCount.get() * 100.0 / processedCount.get()) : 0.0);
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        processedCount.set(0);
        successCount.set(0);
        failureCount.set(0);
    }
}