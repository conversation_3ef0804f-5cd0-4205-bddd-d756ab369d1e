package com.crypto.trading.market.consumer;

import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.dto.market.TradeDTO;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.retrytopic.TopicSuffixingStrategy;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 异步数据库消费者
 * 负责从Kafka消费原始数据并异步写入InfluxDB
 * 
 * 设计原则：
 * 1. 高可靠性 - 手动确认，重试机制，死信队列
 * 2. 高性能 - 批量处理，异步写入
 * 3. 可监控 - 详细的指标统计
 * 4. 容错性 - 完善的错误处理和恢复
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class AsyncDatabaseConsumer {

    private static final Logger log = LoggerFactory.getLogger(AsyncDatabaseConsumer.class);

    @Autowired
    private InfluxDBRepository influxDBRepository;

    @Autowired
    private ObjectMapper objectMapper;

    // 批处理缓冲区
    private final List<KlineDataDTO> klineBatch = new ArrayList<>();
    private final List<DepthDataDTO> depthBatch = new ArrayList<>();
    private final List<TradeDTO> tradeBatch = new ArrayList<>();
    
    // 批处理大小配置
    private static final int BATCH_SIZE = 50;
    private static final int FLUSH_INTERVAL_SECONDS = 5;

    // 统计指标
    private final AtomicLong klineConsumedCount = new AtomicLong(0);
    private final AtomicLong depthConsumedCount = new AtomicLong(0);
    private final AtomicLong tradeConsumedCount = new AtomicLong(0);
    private final AtomicLong klineSuccessCount = new AtomicLong(0);
    private final AtomicLong depthSuccessCount = new AtomicLong(0);
    private final AtomicLong tradeSuccessCount = new AtomicLong(0);
    private final AtomicLong klineFailureCount = new AtomicLong(0);
    private final AtomicLong depthFailureCount = new AtomicLong(0);
    private final AtomicLong tradeFailureCount = new AtomicLong(0);

    // 定时刷新任务
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);

    /**
     * 初始化定时刷新任务
     */
    public void init() {
        // 定时刷新K线数据批次
        scheduler.scheduleAtFixedRate(this::flushKlineBatch, 
            FLUSH_INTERVAL_SECONDS, FLUSH_INTERVAL_SECONDS, TimeUnit.SECONDS);
        
        // 定时刷新深度数据批次
        scheduler.scheduleAtFixedRate(this::flushDepthBatch, 
            FLUSH_INTERVAL_SECONDS, FLUSH_INTERVAL_SECONDS, TimeUnit.SECONDS);
        
        // 定时刷新交易数据批次
        scheduler.scheduleAtFixedRate(this::flushTradeBatch, 
            FLUSH_INTERVAL_SECONDS, FLUSH_INTERVAL_SECONDS, TimeUnit.SECONDS);
        
        log.info("异步数据库消费者初始化完成，批处理大小: {}, 刷新间隔: {}秒", BATCH_SIZE, FLUSH_INTERVAL_SECONDS);
    }

    /**
     * 消费K线原始数据
     */
    @RetryableTopic(
        attempts = "3",
        backoff = @Backoff(delay = 1000, multiplier = 2.0),
        autoCreateTopics = "true",
        topicSuffixingStrategy = TopicSuffixingStrategy.SUFFIX_WITH_INDEX_VALUE,
        dltStrategy = org.springframework.kafka.retrytopic.DltStrategy.FAIL_ON_ERROR,
        include = {Exception.class}
    )
    @KafkaListener(
        topics = "kline.raw.data",
        groupId = "database-writer-group",
        containerFactory = "databaseKafkaListenerContainerFactory"
    )
    public void consumeKlineRawData(
            @Payload Object message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            @Header(KafkaHeaders.RECEIVED_KEY) String key,
            Acknowledgment acknowledgment) {

        try {
            klineConsumedCount.incrementAndGet();

            log.debug("消费K线原始数据: topic={}, partition={}, offset={}, key={}",
                topic, partition, offset, key);

            // 解析消息 - 处理不同的消息类型
            Map<String, Object> messageMap;
            if (message instanceof String) {
                messageMap = objectMapper.readValue((String) message, Map.class);
            } else if (message instanceof Map) {
                messageMap = (Map<String, Object>) message;
            } else {
                // 尝试将对象转换为Map
                messageMap = objectMapper.convertValue(message, Map.class);
            }

            Map<String, Object> dataMap = (Map<String, Object>) messageMap.get("data");

            if (dataMap == null) {
                log.warn("消息中没有data字段: key={}, message={}", key, messageMap);
                acknowledgment.acknowledge();
                return;
            }

            // 转换为KlineDataDTO
            KlineDataDTO klineDataDTO = objectMapper.convertValue(dataMap, KlineDataDTO.class);

            if (klineDataDTO == null) {
                log.warn("K线数据转换失败: key={}, dataMap={}", key, dataMap);
                acknowledgment.acknowledge();
                return;
            }

            // 添加到批处理缓冲区
            synchronized (klineBatch) {
                klineBatch.add(klineDataDTO);

                // 如果达到批处理大小，立即刷新
                if (klineBatch.size() >= BATCH_SIZE) {
                    flushKlineBatch();
                }
            }
            
            // 手动确认消息
            acknowledgment.acknowledge();
            
            log.debug("K线数据消费成功: symbol={}, interval={}", 
                klineDataDTO.getSymbol(), klineDataDTO.getInterval());

        } catch (Exception e) {
            klineFailureCount.incrementAndGet();
            log.error("消费K线数据异常: topic={}, partition={}, offset={}, key={}, error={}", 
                topic, partition, offset, key, e.getMessage(), e);
            
            // 重新抛出异常，触发重试机制
            throw new RuntimeException("消费K线数据失败", e);
        }
    }

    /**
     * 消费深度原始数据
     */
    @RetryableTopic(
        attempts = "3",
        backoff = @Backoff(delay = 1000, multiplier = 2.0),
        autoCreateTopics = "true",
        topicSuffixingStrategy = TopicSuffixingStrategy.SUFFIX_WITH_INDEX_VALUE,
        dltStrategy = org.springframework.kafka.retrytopic.DltStrategy.FAIL_ON_ERROR,
        include = {Exception.class}
    )
    @KafkaListener(
        topics = "depth.raw.data",
        groupId = "database-writer-group",
        containerFactory = "databaseKafkaListenerContainerFactory"
    )
    public void consumeDepthRawData(
            @Payload Object message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            @Header(KafkaHeaders.RECEIVED_KEY) String key,
            Acknowledgment acknowledgment) {

        try {
            depthConsumedCount.incrementAndGet();

            log.debug("消费深度原始数据: topic={}, partition={}, offset={}, key={}",
                topic, partition, offset, key);

            // 解析消息 - 处理不同的消息类型
            Map<String, Object> messageMap;
            if (message instanceof String) {
                messageMap = objectMapper.readValue((String) message, Map.class);
            } else if (message instanceof Map) {
                messageMap = (Map<String, Object>) message;
            } else {
                // 尝试将对象转换为Map
                messageMap = objectMapper.convertValue(message, Map.class);
            }

            Map<String, Object> dataMap = (Map<String, Object>) messageMap.get("data");

            if (dataMap == null) {
                log.warn("消息中没有data字段: key={}, message={}", key, messageMap);
                acknowledgment.acknowledge();
                return;
            }

            // 转换为DepthDataDTO
            DepthDataDTO depthDataDTO = objectMapper.convertValue(dataMap, DepthDataDTO.class);

            if (depthDataDTO == null) {
                log.warn("深度数据转换失败: key={}, dataMap={}", key, dataMap);
                acknowledgment.acknowledge();
                return;
            }

            // 添加到批处理缓冲区
            synchronized (depthBatch) {
                depthBatch.add(depthDataDTO);

                // 如果达到批处理大小，立即刷新
                if (depthBatch.size() >= BATCH_SIZE) {
                    flushDepthBatch();
                }
            }
            
            // 手动确认消息
            acknowledgment.acknowledge();
            
            log.debug("深度数据消费成功: symbol={}", depthDataDTO.getSymbol());

        } catch (Exception e) {
            depthFailureCount.incrementAndGet();
            log.error("消费深度数据异常: topic={}, partition={}, offset={}, key={}, error={}", 
                topic, partition, offset, key, e.getMessage(), e);
            
            // 重新抛出异常，触发重试机制
            throw new RuntimeException("消费深度数据失败", e);
        }
    }

    /**
     * 消费交易原始数据
     */
    @RetryableTopic(
        attempts = "3",
        backoff = @Backoff(delay = 1000, multiplier = 2.0),
        autoCreateTopics = "true",
        topicSuffixingStrategy = TopicSuffixingStrategy.SUFFIX_WITH_INDEX_VALUE,
        dltStrategy = org.springframework.kafka.retrytopic.DltStrategy.FAIL_ON_ERROR,
        include = {Exception.class}
    )
    @KafkaListener(
        topics = "trade.raw.data",
        groupId = "database-writer-group",
        containerFactory = "databaseKafkaListenerContainerFactory"
    )
    public void consumeTradeRawData(
            @Payload Object message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            @Header(KafkaHeaders.RECEIVED_KEY) String key,
            Acknowledgment acknowledgment) {

        try {
            tradeConsumedCount.incrementAndGet();

            log.debug("消费交易原始数据: topic={}, partition={}, offset={}, key={}",
                topic, partition, offset, key);

            // 解析消息 - 处理不同的消息类型
            Map<String, Object> messageMap;
            if (message instanceof String) {
                messageMap = objectMapper.readValue((String) message, Map.class);
            } else if (message instanceof Map) {
                messageMap = (Map<String, Object>) message;
            } else {
                // 尝试将对象转换为Map
                messageMap = objectMapper.convertValue(message, Map.class);
            }

            Map<String, Object> dataMap = (Map<String, Object>) messageMap.get("data");

            if (dataMap == null) {
                log.warn("消息中没有data字段: key={}, message={}", key, messageMap);
                acknowledgment.acknowledge();
                return;
            }

            // 转换为TradeDTO
            TradeDTO tradeDTO = objectMapper.convertValue(dataMap, TradeDTO.class);

            if (tradeDTO == null) {
                log.warn("交易数据转换失败: key={}, dataMap={}", key, dataMap);
                acknowledgment.acknowledge();
                return;
            }

            // 添加到批处理缓冲区
            synchronized (tradeBatch) {
                tradeBatch.add(tradeDTO);

                // 如果达到批处理大小，立即刷新
                if (tradeBatch.size() >= BATCH_SIZE) {
                    flushTradeBatch();
                }
            }
            
            // 手动确认消息
            acknowledgment.acknowledge();
            
            log.debug("交易数据消费成功: symbol={}, tradeId={}", 
                tradeDTO.getSymbol(), tradeDTO.getId());

        } catch (Exception e) {
            tradeFailureCount.incrementAndGet();
            log.error("消费交易数据异常: topic={}, partition={}, offset={}, key={}, error={}", 
                topic, partition, offset, key, e.getMessage(), e);
            
            // 重新抛出异常，触发重试机制
            throw new RuntimeException("消费交易数据失败", e);
        }
    }

    /**
     * 刷新K线数据批次到数据库
     */
    private void flushKlineBatch() {
        List<KlineDataDTO> batchToFlush;
        synchronized (klineBatch) {
            if (klineBatch.isEmpty()) {
                return;
            }
            batchToFlush = new ArrayList<>(klineBatch);
            klineBatch.clear();
        }

        log.debug("开始刷新K线数据批次，数量: {}", batchToFlush.size());

        // 使用虚拟线程异步写入数据库
        CompletableFuture.runAsync(() -> {
            try {
                influxDBRepository.saveKlineDataBatch(batchToFlush);
                klineSuccessCount.addAndGet(batchToFlush.size());
                log.debug("K线数据批次写入成功，数量: {}", batchToFlush.size());
            } catch (Exception e) {
                klineFailureCount.addAndGet(batchToFlush.size());
                log.error("K线数据批次写入失败，数量: {}, 错误: {}",
                    batchToFlush.size(), e.getMessage(), e);

                // 重新加入批次进行重试
                synchronized (klineBatch) {
                    klineBatch.addAll(batchToFlush);
                }
            }
        }, Executors.newVirtualThreadPerTaskExecutor());
    }

    /**
     * 刷新深度数据批次到数据库
     */
    private void flushDepthBatch() {
        List<DepthDataDTO> batchToFlush;
        synchronized (depthBatch) {
            if (depthBatch.isEmpty()) {
                return;
            }
            batchToFlush = new ArrayList<>(depthBatch);
            depthBatch.clear();
        }

        log.debug("开始刷新深度数据批次，数量: {}", batchToFlush.size());

        // 使用虚拟线程异步写入数据库
        CompletableFuture.runAsync(() -> {
            try {
                influxDBRepository.saveDepthDataBatch(batchToFlush);
                depthSuccessCount.addAndGet(batchToFlush.size());
                log.debug("深度数据批次写入成功，数量: {}", batchToFlush.size());
            } catch (Exception e) {
                depthFailureCount.addAndGet(batchToFlush.size());
                log.error("深度数据批次写入失败，数量: {}, 错误: {}",
                    batchToFlush.size(), e.getMessage(), e);

                // 重新加入批次进行重试
                synchronized (depthBatch) {
                    depthBatch.addAll(batchToFlush);
                }
            }
        }, Executors.newVirtualThreadPerTaskExecutor());
    }

    /**
     * 刷新交易数据批次到数据库
     */
    private void flushTradeBatch() {
        List<TradeDTO> batchToFlush;
        synchronized (tradeBatch) {
            if (tradeBatch.isEmpty()) {
                return;
            }
            batchToFlush = new ArrayList<>(tradeBatch);
            tradeBatch.clear();
        }

        log.debug("开始刷新交易数据批次，数量: {}", batchToFlush.size());

        // 使用虚拟线程异步写入数据库
        CompletableFuture.runAsync(() -> {
            try {
                influxDBRepository.saveTradeDataBatch(batchToFlush);
                tradeSuccessCount.addAndGet(batchToFlush.size());
                log.debug("交易数据批次写入成功，数量: {}", batchToFlush.size());
            } catch (Exception e) {
                tradeFailureCount.addAndGet(batchToFlush.size());
                log.error("交易数据批次写入失败，数量: {}, 错误: {}",
                    batchToFlush.size(), e.getMessage(), e);

                // 重新加入批次进行重试
                synchronized (tradeBatch) {
                    tradeBatch.addAll(batchToFlush);
                }
            }
        }, Executors.newVirtualThreadPerTaskExecutor());
    }

    /**
     * 获取统计信息
     */
    public Map<String, Long> getStatistics() {
        Map<String, Long> stats = new ConcurrentHashMap<>();
        stats.put("klineConsumedCount", klineConsumedCount.get());
        stats.put("depthConsumedCount", depthConsumedCount.get());
        stats.put("tradeConsumedCount", tradeConsumedCount.get());
        stats.put("klineSuccessCount", klineSuccessCount.get());
        stats.put("depthSuccessCount", depthSuccessCount.get());
        stats.put("tradeSuccessCount", tradeSuccessCount.get());
        stats.put("klineFailureCount", klineFailureCount.get());
        stats.put("depthFailureCount", depthFailureCount.get());
        stats.put("tradeFailureCount", tradeFailureCount.get());
        stats.put("totalConsumedCount",
            klineConsumedCount.get() + depthConsumedCount.get() + tradeConsumedCount.get());
        stats.put("totalSuccessCount",
            klineSuccessCount.get() + depthSuccessCount.get() + tradeSuccessCount.get());
        stats.put("totalFailureCount",
            klineFailureCount.get() + depthFailureCount.get() + tradeFailureCount.get());
        return stats;
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        klineConsumedCount.set(0);
        depthConsumedCount.set(0);
        tradeConsumedCount.set(0);
        klineSuccessCount.set(0);
        depthSuccessCount.set(0);
        tradeSuccessCount.set(0);
        klineFailureCount.set(0);
        depthFailureCount.set(0);
        tradeFailureCount.set(0);
    }

    /**
     * 销毁方法，清理资源
     */
    public void destroy() {
        // 刷新所有剩余的批次
        flushKlineBatch();
        flushDepthBatch();
        flushTradeBatch();

        // 关闭定时任务
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }

        log.info("异步数据库消费者已销毁");
    }
}
