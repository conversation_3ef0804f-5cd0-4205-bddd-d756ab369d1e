package com.crypto.trading.market.util;

import org.slf4j.Logger;
import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.dto.market.DepthDataDTO;

/**
 * 错误处理工具类
 * 提供统一的数据验证和错误处理方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class ErrorHandlingUtil {
    
    /**
     * 验证K线数据的完整性
     * 
     * @param klineData K线数据
     * @param logger 日志记录器
     * @param context 上下文信息（如key、topic等）
     * @return 验证是否通过
     */
    public static boolean validateKlineData(KlineDataDTO klineData, Logger logger, String context) {
        if (klineData == null) {
            logger.error("K线数据为null: context={}", context);
            return false;
        }
        
        // 验证必要字段
        if (klineData.getSymbol() == null || klineData.getSymbol().trim().isEmpty()) {
            logger.error("K线数据symbol为空: context={}, interval={}, openTime={}", 
                    context, klineData.getInterval(), klineData.getOpenTime());
            return false;
        }
        
        if (klineData.getInterval() == null || klineData.getInterval().trim().isEmpty()) {
            logger.error("K线数据interval为空: context={}, symbol={}, openTime={}", 
                    context, klineData.getSymbol(), klineData.getOpenTime());
            return false;
        }
        
        // 验证时间字段
        if (klineData.getOpenTime() == null) {
            logger.warn("K线数据openTime为null: context={}, symbol={}, interval={}", 
                    context, klineData.getSymbol(), klineData.getInterval());
            // 不返回false，因为我们已经在转换器中处理了这种情况
        }
        
        if (klineData.getCloseTime() == null) {
            logger.warn("K线数据closeTime为null: context={}, symbol={}, interval={}", 
                    context, klineData.getSymbol(), klineData.getInterval());
            // 不返回false，因为我们已经在转换器中处理了这种情况
        }
        
        // 验证价格数据
        if (klineData.getOpen() == null || klineData.getHigh() == null || 
            klineData.getLow() == null || klineData.getClose() == null) {
            logger.warn("K线数据价格字段存在null: context={}, symbol={}, interval={}, open={}, high={}, low={}, close={}", 
                    context, klineData.getSymbol(), klineData.getInterval(),
                    klineData.getOpen(), klineData.getHigh(), klineData.getLow(), klineData.getClose());
            // 不返回false，因为我们在转换器中提供了默认值
        }
        
        return true;
    }
    
    /**
     * 验证深度数据的完整性
     * 
     * @param depthData 深度数据
     * @param logger 日志记录器
     * @param context 上下文信息（如key、topic等）
     * @return 验证是否通过
     */
    public static boolean validateDepthData(DepthDataDTO depthData, Logger logger, String context) {
        if (depthData == null) {
            logger.error("深度数据为null: context={}", context);
            return false;
        }
        
        // 验证必要字段
        if (depthData.getSymbol() == null || depthData.getSymbol().trim().isEmpty()) {
            logger.error("深度数据symbol为空: context={}, lastUpdateId={}, updateTime={}", 
                    context, depthData.getLastUpdateId(), depthData.getUpdateTime());
            return false;
        }
        
        // 验证时间字段
        if (depthData.getUpdateTime() == null) {
            logger.warn("深度数据updateTime为null: context={}, symbol={}, lastUpdateId={}", 
                    context, depthData.getSymbol(), depthData.getLastUpdateId());
            // 不返回false，因为我们在转换器中处理了这种情况
        }
        
        // 验证买卖盘数据
        if ((depthData.getBids() == null || depthData.getBids().isEmpty()) && 
            (depthData.getAsks() == null || depthData.getAsks().isEmpty())) {
            logger.warn("深度数据买卖盘都为空: context={}, symbol={}, lastUpdateId={}", 
                    context, depthData.getSymbol(), depthData.getLastUpdateId());
            // 这种情况下仍然返回true，可能是市场暂停等情况
        }
        
        return true;
    }
    
    /**
     * 记录消息处理开始的信息
     * 
     * @param logger 日志记录器
     * @param messageType 消息类型
     * @param topic Kafka主题
     * @param partition 分区
     * @param offset 偏移量
     * @param key 消息键
     */
    public static void logMessageProcessingStart(Logger logger, String messageType, 
            String topic, int partition, long offset, String key) {
        if (logger.isDebugEnabled()) {
            logger.debug("开始处理{}消息: topic={}, partition={}, offset={}, key={}", 
                    messageType, topic, partition, offset, key);
        }
    }
    
    /**
     * 记录消息处理成功的信息
     * 
     * @param logger 日志记录器
     * @param messageType 消息类型
     * @param key 消息键
     * @param additionalInfo 额外信息
     */
    public static void logMessageProcessingSuccess(Logger logger, String messageType, 
            String key, String additionalInfo) {
        if (logger.isDebugEnabled()) {
            logger.debug("{}消息处理成功: key={}, info={}", messageType, key, additionalInfo);
        }
    }
    
    /**
     * 记录消息处理失败的信息
     * 
     * @param logger 日志记录器
     * @param messageType 消息类型
     * @param key 消息键
     * @param error 错误信息
     * @param exception 异常对象
     */
    public static void logMessageProcessingFailure(Logger logger, String messageType, 
            String key, String error, Exception exception) {
        logger.error("{}消息处理失败: key={}, error={}", messageType, key, error, exception);
    }
    
    /**
     * 安全地获取消息预览（限制长度）
     * 
     * @param message 消息对象
     * @param maxLength 最大长度
     * @return 消息预览字符串
     */
    public static String getMessagePreview(Object message, int maxLength) {
        if (message == null) {
            return "null";
        }
        
        String messageStr = message.toString();
        if (messageStr.length() <= maxLength) {
            return messageStr;
        }
        
        return messageStr.substring(0, maxLength) + "...";
    }
    
    /**
     * 构建上下文信息字符串
     * 
     * @param topic Kafka主题
     * @param partition 分区
     * @param offset 偏移量
     * @param key 消息键
     * @return 上下文信息字符串
     */
    public static String buildContext(String topic, int partition, long offset, String key) {
        return String.format("topic=%s,partition=%d,offset=%d,key=%s", topic, partition, offset, key);
    }
}
