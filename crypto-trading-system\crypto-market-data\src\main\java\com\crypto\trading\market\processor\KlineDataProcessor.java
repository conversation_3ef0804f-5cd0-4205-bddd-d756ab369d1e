package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.AsyncMarketDataProducer;
import com.crypto.trading.sdk.response.model.KlineData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

/**
 * K线数据处理器 - 重构版本
 * 负责处理从WebSocket接收到的K线数据，使用新的异步生产者发送到Kafka
 *
 * 重构要点：
 * 1. 移除虚拟线程直接写入数据库的逻辑
 * 2. 使用AsyncMarketDataProducer发送原始数据到Kafka
 * 3. 完善的错误处理和监控
 * 4. 确保数据不丢失
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class KlineDataProcessor {

    private static final Logger log = LoggerFactory.getLogger(KlineDataProcessor.class);

    @Autowired
    private AsyncMarketDataProducer asyncMarketDataProducer;

    @Autowired
    private MarketDataConverter marketDataConverter;

    // 统计指标
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong successCount = new AtomicLong(0);
    private final AtomicLong failureCount = new AtomicLong(0);

    /**
     * 处理K线数据 - 重构后的异步处理实现
     *
     * @param message WebSocket K线数据消息
     * @param interval K线间隔
     */
    public void processKlineData(WebSocketMessage<KlineData> message, String interval) {
        if (message == null || message.getData() == null) {
            log.warn("收到空的K线数据消息");
            return;
        }

        KlineData klineData = message.getData();
        String symbol = message.getSymbol();

        processedCount.incrementAndGet();

        log.debug("开始处理K线数据: symbol={}, interval={}, openTime={}, closeTime={}",
                symbol, interval, klineData.getOpenTime(), klineData.getCloseTime());

        try {
            // 验证输入参数
            if (message == null) {
                log.error("WebSocket消息为null: symbol={}, interval={}", symbol, interval);
                failureCount.incrementAndGet();
                return;
            }

            if (message.getData() == null) {
                log.error("WebSocket消息数据为null: symbol={}, interval={}, eventType={}",
                        symbol, interval, message.getEventType());
                failureCount.incrementAndGet();
                return;
            }

            log.debug("开始处理K线数据: symbol={}, interval={}, eventTime={}",
                    symbol, interval, message.getEventTime());

            // 转换为KlineDataDTO
            KlineDataDTO klineDataDTO;
            try {
                klineDataDTO = marketDataConverter.convertToKlineDataDTO(klineData, symbol, interval);
            } catch (Exception e) {
                log.error("K线数据转换异常: symbol={}, interval={}, error={}",
                        symbol, interval, e.getMessage(), e);
                failureCount.incrementAndGet();
                return;
            }

            if (klineDataDTO == null) {
                log.warn("K线数据转换返回null: symbol={}, interval={}, eventType={}",
                        symbol, interval, message.getEventType());
                failureCount.incrementAndGet();
                return;
            }

            // 验证转换后的数据
            if (klineDataDTO.getSymbol() == null || klineDataDTO.getInterval() == null) {
                log.error("转换后的K线数据关键字段为null: symbol={}, interval={}, originalSymbol={}, originalInterval={}",
                        klineDataDTO.getSymbol(), klineDataDTO.getInterval(), symbol, interval);
                failureCount.incrementAndGet();
                return;
            }

            // 使用异步生产者发送原始数据到Kafka
            CompletableFuture<SendResult<String, Object>> future =
                asyncMarketDataProducer.sendKlineRawData(klineDataDTO);

            // 添加回调处理
            future.whenComplete((result, throwable) -> {
                if (throwable == null) {
                    successCount.incrementAndGet();
                    log.debug("K线数据发送成功: symbol={}, interval={}, partition={}, offset={}",
                        symbol, interval, result.getRecordMetadata().partition(),
                        result.getRecordMetadata().offset());
                } else {
                    failureCount.incrementAndGet();
                    log.error("K线数据发送失败: symbol={}, interval={}, error={}",
                        symbol, interval, throwable.getMessage(), throwable);

                    // 这里可以添加重试逻辑或者将失败的数据发送到死信队列
                    handleSendFailure(klineDataDTO, throwable);
                }
            });

            log.debug("K线数据已提交到异步队列: symbol={}, interval={}, openTime={}",
                    symbol, interval, klineDataDTO.getOpenTime());

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("处理K线数据异常: symbol={}, interval={}, error={}",
                symbol, interval, e.getMessage(), e);

            // 记录处理失败的数据，可以考虑重试或发送到死信队列
            // 在这个版本中，我们不阻塞主流程，但会记录详细的错误信息
        }
    }

    /**
     * 处理发送失败的情况
     *
     * @param klineDataDTO 失败的K线数据
     * @param throwable 异常信息
     */
    private void handleSendFailure(KlineDataDTO klineDataDTO, Throwable throwable) {
        // 这里可以实现重试逻辑或者将失败的数据发送到死信队列
        // 目前先记录详细的错误信息
        log.error("K线数据发送失败，需要人工处理: symbol={}, interval={}, openTime={}, error={}",
            klineDataDTO.getSymbol(), klineDataDTO.getInterval(),
            klineDataDTO.getOpenTime(), throwable.getMessage());

        // TODO: 实现失败数据的恢复机制
        // 1. 可以将失败的数据写入本地文件
        // 2. 可以发送到死信队列
        // 3. 可以实现重试机制
    }

    /**
     * 获取处理统计信息
     *
     * @return 统计信息
     */
    public String getStatistics() {
        return String.format("KlineDataProcessor统计: 处理总数=%d, 成功=%d, 失败=%d, 成功率=%.2f%%",
            processedCount.get(), successCount.get(), failureCount.get(),
            processedCount.get() > 0 ? (successCount.get() * 100.0 / processedCount.get()) : 0.0);
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        processedCount.set(0);
        successCount.set(0);
        failureCount.set(0);
    }
} 